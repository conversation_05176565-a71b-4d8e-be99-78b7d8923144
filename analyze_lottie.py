import json

# Load and analyze the Lottie file
with open('mainlottie.json', 'r') as f:
    lottie_data = json.load(f)

print("Lottie Animation Analysis:")
print(f"Total layers: {len(lottie_data['layers'])}")
print("\nLayer breakdown by position and color:")

for i, layer in enumerate(lottie_data['layers']):
    if 'shapes' in layer and layer['shapes']:
        # Get the transform anchor point (position)
        anchor = layer['shapes'][0]['it'][-1]['a']['k'] if 'a' in layer['shapes'][0]['it'][-1] else [0, 0]
        
        # Get color if available
        color = None
        for item in layer['shapes'][0]['it']:
            if item.get('ty') == 'fl' and 'c' in item:
                color = item['c']['k']
                break
        
        print(f"Layer {i} (ind: {layer['ind']}): Position {anchor}, Color: {color}")

# Look for potential hand/arm layers (typically on the sides)
print("\nPotential hand/arm candidates (based on X position):")
for i, layer in enumerate(lottie_data['layers']):
    if 'shapes' in layer and layer['shapes']:
        anchor = layer['shapes'][0]['it'][-1]['a']['k'] if 'a' in layer['shapes'][0]['it'][-1] else [0, 0]
        x_pos = anchor[0]
        
        # Assuming hands/arms are on the far left or right
        if x_pos < 600 or x_pos > 1200:
            print(f"Layer {i}: X={x_pos} - Potential hand/arm")

print("\nPotential face/head candidates (based on Y position - upper area):")
for i, layer in enumerate(lottie_data['layers']):
    if 'shapes' in layer and layer['shapes']:
        anchor = layer['shapes'][0]['it'][-1]['a']['k'] if 'a' in layer['shapes'][0]['it'][-1] else [0, 0]
        y_pos = anchor[1]
        
        # Assuming face/head is in upper area
        if y_pos < 1300:
            print(f"Layer {i}: Y={y_pos} - Potential face/head")
