<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Character Animation - Working Version</title>
    <script src="https://unpkg.com/lottie-web@5.12.2/build/player/lottie.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            text-align: center;
            max-width: 500px;
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .animation-container {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 20px;
            margin: 20px 0;
            border: 3px solid #e9ecef;
        }
        .controls {
            margin-top: 20px;
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
        }
        button {
            background: linear-gradient(45deg, #2c6e6e, #1a4a4a);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(44, 110, 110, 0.3);
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(44, 110, 110, 0.4);
        }
        button:active {
            transform: translateY(0);
        }
        .features {
            margin-top: 25px;
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #2c6e6e;
        }
        .features h3 {
            color: #2c6e6e;
            margin-top: 0;
            margin-bottom: 15px;
        }
        .features ul {
            margin: 0;
            padding-left: 20px;
        }
        .features li {
            margin-bottom: 8px;
            color: #555;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 8px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 Character Animation</h1>
        <p class="subtitle">Lottie animation with hand waving, blinking, and smiling</p>
        
        <div class="animation-container">
            <div id="lottie-container" style="width: 400px; height: 600px; margin: 0 auto;"></div>
        </div>
        
        <div class="controls">
            <button onclick="playAnimation()">▶️ Play</button>
            <button onclick="pauseAnimation()">⏸️ Pause</button>
            <button onclick="stopAnimation()">⏹️ Stop</button>
            <button onclick="toggleLoop()">🔄 Toggle Loop</button>
            <button onclick="changeSpeed()">⚡ Speed</button>
        </div>
        
        <div id="status" class="status success">
            Animation Ready! 🎉
        </div>
        
        <div class="features">
            <h3>✨ Animation Features:</h3>
            <ul>
                <li><strong>👋 Hand Waving:</strong> Right arm waves back and forth continuously</li>
                <li><strong>👁️ Blinking:</strong> Eyes blink naturally at frame 80-100</li>
                <li><strong>😊 Smiling:</strong> Smile grows from small to full size</li>
                <li><strong>🎨 Character Design:</strong> Teal dress and headband, brown skin tone</li>
                <li><strong>⏱️ Duration:</strong> 6 seconds (180 frames at 30fps)</li>
                <li><strong>🔄 Seamless Loop:</strong> Perfect for continuous playback</li>
            </ul>
        </div>
    </div>

    <script>
        let animation;
        let currentSpeed = 1;
        
        // Load animation when page loads
        window.addEventListener('load', function() {
            fetch('character_simple.json')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Failed to load animation file');
                    }
                    return response.json();
                })
                .then(animationData => {
                    animation = lottie.loadAnimation({
                        container: document.getElementById('lottie-container'),
                        renderer: 'svg',
                        loop: true,
                        autoplay: true,
                        animationData: animationData
                    });
                    
                    updateStatus('Animation loaded successfully! 🎉', 'success');
                    console.log('Animation loaded successfully');
                    
                    // Add event listeners
                    animation.addEventListener('complete', function() {
                        console.log('Animation cycle complete');
                    });
                    
                    animation.addEventListener('loopComplete', function() {
                        console.log('Animation loop complete');
                    });
                })
                .catch(error => {
                    console.error('Error loading animation:', error);
                    updateStatus('Error loading animation: ' + error.message, 'error');
                });
        });
        
        function updateStatus(message, type) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = 'status ' + type;
        }
        
        function playAnimation() {
            if (animation) {
                animation.play();
                updateStatus('Animation playing ▶️', 'success');
            }
        }
        
        function pauseAnimation() {
            if (animation) {
                animation.pause();
                updateStatus('Animation paused ⏸️', 'success');
            }
        }
        
        function stopAnimation() {
            if (animation) {
                animation.stop();
                updateStatus('Animation stopped ⏹️', 'success');
            }
        }
        
        function toggleLoop() {
            if (animation) {
                animation.loop = !animation.loop;
                const loopStatus = animation.loop ? 'enabled' : 'disabled';
                updateStatus(`Loop ${loopStatus} 🔄`, 'success');
                console.log('Loop:', animation.loop);
            }
        }
        
        function changeSpeed() {
            if (animation) {
                currentSpeed = currentSpeed === 1 ? 2 : currentSpeed === 2 ? 0.5 : 1;
                animation.setSpeed(currentSpeed);
                updateStatus(`Speed set to ${currentSpeed}x ⚡`, 'success');
                console.log('Speed:', currentSpeed);
            }
        }
    </script>
</body>
</html>
