<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Character Animation Test</title>
    <script src="https://unpkg.com/lottie-web@5.12.2/build/player/lottie.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .controls {
            margin-top: 20px;
        }
        button {
            background: #2c6e6e;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1a4a4a;
        }
        .description {
            margin-top: 20px;
            color: #666;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Character Animation</h1>

        <div id="lottie-container" style="width: 400px; height: 600px; margin: 0 auto;"></div>

        <div class="controls">
            <button onclick="playAnimation()">Play</button>
            <button onclick="pauseAnimation()">Pause</button>
            <button onclick="stopAnimation()">Stop</button>
            <button onclick="toggleLoop()">Toggle Loop</button>
        </div>

        <div class="description">
            <h3>Animation Features:</h3>
            <ul style="text-align: left;">
                <li><strong>Hand Waving:</strong> Right arm waves back and forth continuously</li>
                <li><strong>Blinking:</strong> Eyes blink at frame 80-100</li>
                <li><strong>Smiling:</strong> Smile grows from small to full size</li>
                <li><strong>Character Design:</strong> Based on the original image with teal dress and headband</li>
            </ul>
        </div>
    </div>

    <script>
        let animation;

        // Load animation data
        fetch('character_animation.json')
            .then(response => response.json())
            .then(animationData => {
                animation = lottie.loadAnimation({
                    container: document.getElementById('lottie-container'),
                    renderer: 'svg',
                    loop: true,
                    autoplay: true,
                    animationData: animationData
                });
                console.log('Animation loaded successfully');
            })
            .catch(error => {
                console.error('Error loading animation:', error);
                // Fallback: Load animation data inline
                loadInlineAnimation();
            });

        function loadInlineAnimation() {
            // Simplified animation data embedded directly
            const simpleAnimationData = {
                "v": "5.7.4",
                "fr": 30,
                "ip": 0,
                "op": 180,
                "w": 400,
                "h": 600,
                "nm": "Character Animation",
                "ddd": 0,
                "assets": [],
                "layers": [
                    {
                        "ddd": 0,
                        "ind": 1,
                        "ty": 4,
                        "nm": "Background",
                        "sr": 1,
                        "ks": {
                            "o": {"a": 0, "k": 100},
                            "r": {"a": 0, "k": 0},
                            "p": {"a": 0, "k": [200, 300]},
                            "a": {"a": 0, "k": [0, 0]},
                            "s": {"a": 0, "k": [100, 100]}
                        },
                        "ao": 0,
                        "shapes": [
                            {
                                "ty": "gr",
                                "it": [
                                    {
                                        "ty": "rc",
                                        "d": 1,
                                        "s": {"a": 0, "k": [400, 600]},
                                        "p": {"a": 0, "k": [0, 0]},
                                        "r": {"a": 0, "k": 0}
                                    },
                                    {
                                        "ty": "fl",
                                        "c": {"a": 0, "k": [0.96, 0.94, 0.88, 1]},
                                        "o": {"a": 0, "k": 100}
                                    },
                                    {
                                        "ty": "tr",
                                        "p": {"a": 0, "k": [0, 0]},
                                        "a": {"a": 0, "k": [0, 0]},
                                        "s": {"a": 0, "k": [100, 100]},
                                        "r": {"a": 0, "k": 0},
                                        "o": {"a": 0, "k": 100}
                                    }
                                ]
                            }
                        ],
                        "ip": 0,
                        "op": 180,
                        "st": 0
                    },
                    {
                        "ddd": 0,
                        "ind": 2,
                        "ty": 4,
                        "nm": "Waving Circle",
                        "sr": 1,
                        "ks": {
                            "o": {"a": 0, "k": 100},
                            "r": {
                                "a": 1,
                                "k": [
                                    {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [0]},
                                    {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [45]},
                                    {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [-30]},
                                    {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [45]},
                                    {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 120, "s": [-30]},
                                    {"t": 150, "s": [0]}
                                ]
                            },
                            "p": {"a": 0, "k": [300, 200]},
                            "a": {"a": 0, "k": [0, 0]},
                            "s": {"a": 0, "k": [100, 100]}
                        },
                        "ao": 0,
                        "shapes": [
                            {
                                "ty": "gr",
                                "it": [
                                    {
                                        "ty": "el",
                                        "d": 1,
                                        "s": {"a": 0, "k": [50, 50]},
                                        "p": {"a": 0, "k": [0, 0]}
                                    },
                                    {
                                        "ty": "fl",
                                        "c": {"a": 0, "k": [0.2, 0.6, 0.6, 1]},
                                        "o": {"a": 0, "k": 100}
                                    },
                                    {
                                        "ty": "tr",
                                        "p": {"a": 0, "k": [0, 0]},
                                        "a": {"a": 0, "k": [0, 0]},
                                        "s": {"a": 0, "k": [100, 100]},
                                        "r": {"a": 0, "k": 0},
                                        "o": {"a": 0, "k": 100}
                                    }
                                ]
                            }
                        ],
                        "ip": 0,
                        "op": 180,
                        "st": 0
                    }
                ]
            };

            animation = lottie.loadAnimation({
                container: document.getElementById('lottie-container'),
                renderer: 'svg',
                loop: true,
                autoplay: true,
                animationData: simpleAnimationData
            });
            console.log('Fallback animation loaded');
        }

        function playAnimation() {
            if (animation) animation.play();
        }

        function pauseAnimation() {
            if (animation) animation.pause();
        }

        function stopAnimation() {
            if (animation) animation.stop();
        }

        function toggleLoop() {
            if (animation) {
                animation.loop = !animation.loop;
                console.log('Loop:', animation.loop);
            }
        }
    </script>
</body>
</html>
