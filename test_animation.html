<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Character Animation Test</title>
    <script src="https://unpkg.com/@lottiefiles/lottie-player@latest/dist/lottie-player.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f0f0f0;
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            text-align: center;
        }
        h1 {
            color: #333;
            margin-bottom: 20px;
        }
        .controls {
            margin-top: 20px;
        }
        button {
            background: #2c6e6e;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        button:hover {
            background: #1a4a4a;
        }
        .description {
            margin-top: 20px;
            color: #666;
            max-width: 400px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Character Animation</h1>
        
        <lottie-player
            id="character-animation"
            src="character_animation.json"
            background="transparent"
            speed="1"
            style="width: 400px; height: 600px;"
            loop
            autoplay>
        </lottie-player>
        
        <div class="controls">
            <button onclick="playAnimation()">Play</button>
            <button onclick="pauseAnimation()">Pause</button>
            <button onclick="stopAnimation()">Stop</button>
            <button onclick="toggleLoop()">Toggle Loop</button>
        </div>
        
        <div class="description">
            <h3>Animation Features:</h3>
            <ul style="text-align: left;">
                <li><strong>Hand Waving:</strong> Right arm waves back and forth continuously</li>
                <li><strong>Blinking:</strong> Eyes blink at frame 80-100</li>
                <li><strong>Smiling:</strong> Smile grows from small to full size</li>
                <li><strong>Character Design:</strong> Based on the original image with teal dress and headband</li>
            </ul>
        </div>
    </div>

    <script>
        const player = document.getElementById('character-animation');
        
        function playAnimation() {
            player.play();
        }
        
        function pauseAnimation() {
            player.pause();
        }
        
        function stopAnimation() {
            player.stop();
        }
        
        function toggleLoop() {
            player.loop = !player.loop;
            console.log('Loop:', player.loop);
        }
        
        // Log animation events
        player.addEventListener('ready', () => {
            console.log('Animation ready');
        });
        
        player.addEventListener('complete', () => {
            console.log('Animation complete');
        });
    </script>
</body>
</html>
